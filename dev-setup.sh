#!/bin/bash

# Development setup script using uv

echo "🔧 Setting up Antsomi Generative AI API for local development..."

# Check if uv is installed
if ! command -v uv &> /dev/null; then
    echo "❌ uv is not installed!"
    echo "Installing uv..."
    curl -LsSf https://astral.sh/uv/install.sh | sh
    source $HOME/.cargo/env
fi

# Check if uv is now available
if ! command -v uv &> /dev/null; then
    echo "❌ Failed to install uv. Please install manually:"
    echo "curl -LsSf https://astral.sh/uv/install.sh | sh"
    exit 1
fi

echo "✅ uv is available"

# Install dependencies
echo "📦 Installing dependencies with uv..."
uv sync

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    if [ -f .env.dev.example ]; then
        echo "📝 Creating .env file from template..."
        cp .env.dev.example .env
        echo "⚠️  Please edit .env file and update the API keys and configurations"
    else
        echo "📝 Creating basic .env file..."
        cat > .env << EOF
APPLICATION_ENV=development
PORT=5000
API_KEY=your_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
BROKER_URL=redis://localhost:6379/0
RESULT_BACKEND=redis://localhost:6379/0
APP_NAME=Antsomi Generative AI API
EOF
        echo "⚠️  Please edit .env file and update the API keys"
    fi
fi

# Create log directory
mkdir -p log

echo "✅ Development setup completed!"
echo ""
echo "Next steps:"
echo "1. Edit .env file and update API keys"
echo "2. Start Redis: docker run -d --name redis -p 6379:6379 redis"
echo "3. Run the app: uv run python run.py"
echo "4. Run celery worker (optional): uv run celery -A celery_worker.celery worker --loglevel=INFO"
echo ""
echo "Or use Docker setup:"
echo "1. ./start.sh"
