version: "3.8"

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile-dev
    environment:
      - APPLICATION_ENV=development
      - PORT=5001
      - PYTHONUNBUFFERED=1
      # - export PYTHONPATH=/app:$PYTHONPATH
    ports:
      - "5001:5001"
    depends_on:
      - ai-redis
      - ai-postgres
    volumes:
      - .:/app
      - /app/venv
    restart: always

  ai-redis:
    image: redis:latest
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    restart: always

  ai-postgres:
    image: postgres:12.1
    environment:
      - POSTGRES_DB=genai
      - POSTGRES_USER=genai
      - POSTGRES_PASSWORD=AMads@2024
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
    restart: always

volumes:
  redis-data:
  postgres-data:

