FROM python:3.10

RUN apt-get update && apt-get install -y \
    python3-dev \
    libpq-dev \
    gcc \
    vim \
    curl \
    wget \
    net-tools

# Create app directory
WORKDIR /app

ENV APPLICATION_ENV=development

# Install app dependencies
COPY ./requirements.txt ./

RUN pip install --progress-bar off -r requirements.txt

# Bundle app source
COPY . /app

EXPOSE ${PORT}
CMD [ "python", "run.py" ]
