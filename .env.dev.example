# Development Environment Variables Template
# Copy this file to .env.dev and update the values

APPLICATION_ENV=development
PORT=5001

# API Keys - Replace with your actual keys
API_KEY=your_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# Redis Configuration (using Docker service names)
BROKER_URL=redis://ai-redis:6379/0
RESULT_BACKEND=redis://ai-redis:6379/0

# Database Configuration (using Docker service names)
DATABASE_URL=**********************************************/genai

# App Configuration
APP_NAME=Antsomi Generative AI API
PYTHONUNBUFFERED=1
