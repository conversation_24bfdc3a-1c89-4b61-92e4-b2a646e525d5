#!/bin/bash

# Start script for development environment

echo "🚀 Starting Antsomi Generative AI API in Development Mode..."

# Check if .env.dev exists
if [ ! -f .env.dev ]; then
    echo "❌ .env.dev file not found!"
    echo "Please create .env.dev file with required environment variables."
    echo "You can copy from .env.dev.example if available."
    exit 1
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running!"
    echo "Please start Docker and try again."
    exit 1
fi

# Build and start services
echo "🔨 Building and starting services..."
docker-compose -f docker-compose.dev.yml up --build -d

# Wait a moment for services to start
echo "⏳ Waiting for services to start..."
sleep 10

# Check if services are running
echo "🔍 Checking service status..."
docker-compose -f docker-compose.dev.yml ps

# Show logs
echo "📋 Showing application logs (press Ctrl+C to stop)..."
echo "App will be available at: http://localhost:5001"
echo "Redis will be available at: localhost:6379"
echo "PostgreSQL will be available at: localhost:5432"
echo ""

# Follow logs
docker-compose -f docker-compose.dev.yml logs -f app
